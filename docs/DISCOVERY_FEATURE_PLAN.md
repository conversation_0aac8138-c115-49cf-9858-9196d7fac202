# 📱 DISCOVERY FEATURE - TIKTOK-STYLE VOCABULARY LEARNING

## 🎯 TỔNG QUAN TÍNH NĂNG

### Mô tả
Tính năng Discovery mang đến trải nghiệm học từ vựng tương tự TikTok/Instagram Reels/YouTube Shorts, cho phép người dùng khám phá từ vựng mới một cách thú vị và tương tác.

### Mục tiêu
- Tăng engagement và thời gian sử dụng app
- Giúp người dùng khám phá từ vựng mới một cách tự nhiên
- Tạo trải nghiệm học tập giải trí và không áp lực
- Cung cấp hệ thống gợi ý thông minh dựa trên AI

### Luồng người dùng
1. Người dùng nhấn nút "Discovery" trên trang chủ
2. Mở dialog toàn màn hình với giao diện giống TikTok
3. Hiển thị từ vựng đầu tiên với animation mượt mà
4. Người dùng có thể:
   - Vuốt lên/xuống để xem từ tiếp theo/trước đó
   - Nhấn nút tim để lưu vào wishlist
   - Nhấn nút lưu để chọn collection cụ thể
   - Đóng dialog để quay về trang chủ

## 🗄️ CẬP NHẬT DATABASE SCHEMA

### 1. Thêm Collection Type
```sql
-- Thêm enum CollectionType
enum CollectionType {
  NORMAL
  WISHLIST
}

-- Cập nhật Collection model
model Collection {
  id                             String            @id() @default(uuid())
  name                           String
  type                           CollectionType    @default(NORMAL)  // NEW FIELD
  target_language                Language?         // NULLABLE for wishlist
  source_language                Language?         // NULLABLE for wishlist
  user                           User              @relation(fields: [user_id], references: [id], onDelete: Cascade)
  user_id                        String
  word_ids                       String[]
  paragraph_ids                  String[]
  keyword_ids                    String[]
  enable_learn_word_notification Boolean           @default(false)
  collection_stats               CollectionStats[]
  created_at                     DateTime          @default(now())
  updated_at                     DateTime          @updatedAt()

  @@index([user_id])
  @@index([type])
}
```

### 2. Discovery Suggestions Table
```sql
model DiscoverySuggestion {
  id              String   @id @default(uuid())
  user_id         String
  word_id         String
  score           Float    // Điểm gợi ý (0-1)
  reason          String   // Lý do gợi ý: "trending", "difficulty_match", "topic_interest", etc.
  metadata        Json     @default("{}")  // Thông tin bổ sung
  suggested_at    DateTime @default(now())
  viewed_at       DateTime?
  interacted_at   DateTime?
  interaction_type String? // "liked", "saved", "skipped"
  
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)
  
  @@unique([user_id, word_id])
  @@index([user_id])
  @@index([score])
  @@index([suggested_at])
}
```

### 3. User Preferences Table
```sql
model UserDiscoveryPreference {
  id                    String   @id @default(uuid())
  user_id               String   @unique
  preferred_languages   Language[]
  difficulty_preference Difficulty[]
  topics_of_interest    String[] // ["business", "travel", "technology", etc.]
  daily_discovery_limit Int      @default(50)
  auto_save_liked       Boolean  @default(true)
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt()
  
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)
}
```

## 🔧 BACKEND IMPLEMENTATION

### 1. Discovery Service
```typescript
// src/backend/services/discovery.service.ts
export interface DiscoveryService {
  // Lấy từ gợi ý cho user
  getSuggestedWords(userId: string, limit?: number): Promise<WordDetail[]>;
  
  // Ghi nhận tương tác của user
  recordInteraction(
    userId: string, 
    wordId: string, 
    interactionType: 'viewed' | 'liked' | 'saved' | 'skipped'
  ): Promise<void>;
  
  // Cập nhật preferences của user
  updateUserPreferences(userId: string, preferences: Partial<UserDiscoveryPreference>): Promise<void>;
  
  // Tạo wishlist collection cho user mới
  createWishlistCollection(userId: string): Promise<Collection>;
  
  // Lấy wishlist collection của user
  getUserWishlistCollection(userId: string): Promise<Collection>;
}
```

### 2. Suggestion Algorithm Service
```typescript
// src/backend/services/suggestion-algorithm.service.ts
export interface SuggestionAlgorithmService {
  // Tính điểm gợi ý cho từ
  calculateSuggestionScore(userId: string, word: WordDetail): Promise<number>;
  
  // Lấy từ trending
  getTrendingWords(language?: Language, limit?: number): Promise<WordDetail[]>;
  
  // Lấy từ phù hợp với level
  getWordsForDifficulty(difficulty: Difficulty, language?: Language): Promise<WordDetail[]>;
  
  // Lấy từ theo chủ đề
  getWordsByTopic(topic: string, language?: Language): Promise<WordDetail[]>;
  
  // Refresh suggestions cho user
  refreshUserSuggestions(userId: string): Promise<void>;
}
```

### 3. API Endpoints
```typescript
// src/app/api/discovery/suggestions/route.ts
GET /api/discovery/suggestions
- Query: limit, refresh
- Response: WordDetail[]

// src/app/api/discovery/interact/route.ts  
POST /api/discovery/interact
- Body: { wordId, interactionType }
- Response: { success: boolean }

// src/app/api/discovery/preferences/route.ts
GET /api/discovery/preferences
PUT /api/discovery/preferences
- Body: UserDiscoveryPreference
- Response: UserDiscoveryPreference

// src/app/api/discovery/wishlist/route.ts
GET /api/discovery/wishlist
POST /api/discovery/wishlist/add
- Body: { wordId }
- Response: Collection
```

## 🎨 FRONTEND IMPLEMENTATION

### 1. Discovery Dialog Component
```typescript
// src/components/discovery/discovery-dialog.tsx
interface DiscoveryDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function DiscoveryDialog({ isOpen, onClose }: DiscoveryDialogProps) {
  // Full-screen dialog với animation
  // Swipe gestures support
  // Keyboard navigation
  // Loading states
}
```

### 2. Word Card Component
```typescript
// src/components/discovery/word-card.tsx
interface WordCardProps {
  word: WordDetail;
  onLike: (wordId: string) => void;
  onSave: (wordId: string) => void;
  onNext: () => void;
  onPrevious: () => void;
}

export function WordCard({ word, onLike, onSave, onNext, onPrevious }: WordCardProps) {
  // TikTok-style card layout
  // Pronunciation audio
  // Definition display
  // Example sentences
  // Action buttons (heart, save)
}
```

### 3. Collection Selector
```typescript
// src/components/discovery/collection-selector.tsx
interface CollectionSelectorProps {
  word: WordDetail;
  onSelect: (collectionId: string) => void;
  onClose: () => void;
}

export function CollectionSelector({ word, onSelect, onClose }: CollectionSelectorProps) {
  // Filter collections by language compatibility
  // Create new collection option
  // Search collections
}
```

### 4. Home Page Integration
```typescript
// Thêm vào src/app/home.client.tsx
<Button
  size="lg"
  variant="outline"
  className="w-full border-2 border-primary/20 hover:border-primary/40"
  onClick={() => setDiscoveryOpen(true)}
>
  <Sparkles className="size-5 mr-2" />
  <Translate text="home.discovery" />
  <ArrowRight className="size-5 ml-2" />
</Button>
```

## 🧠 HỆ THỐNG GỢI Ý THÔNG MINH

### 1. Thuật toán Scoring
```typescript
interface SuggestionFactors {
  userLevel: number;        // 0-1, dựa trên từ đã học
  wordDifficulty: number;   // 0-1, từ CEFR level
  topicRelevance: number;   // 0-1, dựa trên interests
  trendingScore: number;    // 0-1, từ phổ biến
  diversityBonus: number;   // 0-1, khuyến khích đa dạng
  recencyPenalty: number;   // 0-1, tránh lặp lại gần đây
}

function calculateScore(factors: SuggestionFactors): number {
  return (
    factors.userLevel * 0.25 +
    factors.wordDifficulty * 0.20 +
    factors.topicRelevance * 0.20 +
    factors.trendingScore * 0.15 +
    factors.diversityBonus * 0.10 +
    factors.recencyPenalty * 0.10
  );
}
```

### 2. Personalization Engine
- **Learning History Analysis**: Phân tích từ đã học để xác định level
- **Interaction Patterns**: Theo dõi loại từ user thích
- **Time-based Preferences**: Gợi ý khác nhau theo thời gian trong ngày
- **Spaced Repetition Integration**: Kết hợp với hệ thống ôn tập

### 3. Content Curation
- **CEFR Level Matching**: Gợi ý từ phù hợp với trình độ
- **Topic Clustering**: Nhóm từ theo chủ đề (business, travel, etc.)
- **Trending Analysis**: Từ được tương tác nhiều gần đây
- **Semantic Similarity**: Từ liên quan đến những từ đã học

## 🔄 INTEGRATION VỚI HỆ THỐNG HIỆN TẠI

### 1. Collection Service Updates
```typescript
// Thêm methods vào CollectionService
createWishlistCollection(userId: string): Promise<Collection>;
getUserWishlistCollection(userId: string): Promise<Collection>;
addWordToWishlist(userId: string, wordId: string): Promise<void>;
getCompatibleCollections(userId: string, word: WordDetail): Promise<Collection[]>;
```

### 2. User Service Updates
```typescript
// Thêm methods vào UserService
initializeUserDiscoveryPreferences(userId: string): Promise<UserDiscoveryPreference>;
getUserDiscoveryPreferences(userId: string): Promise<UserDiscoveryPreference>;
updateDiscoveryPreferences(userId: string, preferences: Partial<UserDiscoveryPreference>): Promise<void>;
```

### 3. Word Service Updates
```typescript
// Thêm methods vào WordService
getRandomWordsForDiscovery(userId: string, limit: number): Promise<WordDetail[]>;
getWordsByDifficultyRange(minLevel: string, maxLevel: string): Promise<WordDetail[]>;
getWordsByTopics(topics: string[]): Promise<WordDetail[]>;
```

## 🧪 TESTING STRATEGY

### 1. Unit Tests
- Discovery Service methods
- Suggestion Algorithm logic
- Collection type handling
- User preferences management

### 2. Integration Tests
- API endpoints functionality
- Database operations
- Service interactions
- Authentication flow

### 3. E2E Tests
```typescript
// e2e/discovery-workflow.spec.ts
test('Discovery workflow', async ({ page }) => {
  // Login user
  // Open discovery dialog
  // Interact with words
  // Save to collections
  // Verify data persistence
});
```

### 4. Performance Tests
- Suggestion generation speed
- Large dataset handling
- Concurrent user support
- Memory usage optimization

## 🚀 DEPLOYMENT PLAN

### Phase 1: Core Infrastructure (Week 1-2)
- [ ] Database schema updates
- [ ] Basic Discovery Service
- [ ] Wishlist collection creation
- [ ] API endpoints setup

### Phase 2: Frontend Implementation (Week 3-4)
- [ ] Discovery Dialog component
- [ ] Word Card component
- [ ] Collection Selector
- [ ] Home page integration

### Phase 3: Smart Suggestions (Week 5-6)
- [ ] Suggestion Algorithm Service
- [ ] Personalization engine
- [ ] Content curation logic
- [ ] Performance optimization

### Phase 4: Testing & Polish (Week 7-8)
- [ ] Comprehensive testing
- [ ] UI/UX refinements
- [ ] Performance tuning
- [ ] Documentation updates

### Phase 5: Launch & Monitor (Week 9)
- [ ] Production deployment
- [ ] User feedback collection
- [ ] Analytics implementation
- [ ] Bug fixes and improvements

## 📊 SUCCESS METRICS

### Engagement Metrics
- Daily active users in Discovery
- Average session duration
- Words viewed per session
- Interaction rate (like/save/skip)

### Learning Metrics
- Words added to collections
- Retention rate of discovered words
- Learning progress improvement
- User satisfaction scores

### Technical Metrics
- API response times
- Suggestion accuracy
- Error rates
- System performance

## 🔮 FUTURE ENHANCEMENTS

### Advanced Features
- **AI-Generated Content**: Tạo câu ví dụ và định nghĩa bằng AI
- **Social Features**: Chia sẻ từ yêu thích với bạn bè
- **Gamification**: Streak, badges, leaderboards
- **Voice Recognition**: Luyện phát âm trực tiếp

### Personalization
- **Learning Style Adaptation**: Điều chỉnh theo cách học của user
- **Contextual Suggestions**: Gợi ý dựa trên thời gian, địa điểm
- **Collaborative Filtering**: Gợi ý dựa trên user tương tự
- **Adaptive Difficulty**: Tự động điều chỉnh độ khó

---

**Tài liệu này sẽ được cập nhật thường xuyên trong quá trình phát triển tính năng Discovery.**
