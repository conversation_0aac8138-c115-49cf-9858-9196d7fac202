import { Language, PartsOfSpeech, Prisma } from '@prisma/client';
import { z } from 'zod';

export const WordNetDataSchema = z.object({
	synsets: z.array(z.string()),
	lemma: z.string().nullable(),
	hypernyms: z.array(z.string()),
	hyponyms: z.array(z.string()),
	holonyms: z.array(z.string()),
	meronyms: z.array(z.string()),
});

export const RandomWordSchema = z.object({
	term: z.string(),
	partOfSpeech: z.array(z.nativeEnum(PartsOfSpeech)),
	meaning: z.array(
		z.object({
			EN: z.string(),
			VI: z.string(),
		})
	),
	wordnet_data: WordNetDataSchema.nullable().optional(),
});

export type RandomWord = z.infer<typeof RandomWordSchema>;

export const RandomWordDetailSchema = z.object({
	term: z.string(),
	language: z.nativeEnum(Language),
	definitions: z.array(
		z.object({
			pos: z.array(z.nativeEnum(PartsOfSpeech)),
			ipa: z.string(),
			explains: z.array(
				z.object({
					EN: z.string(),
					VI: z.string(),
				})
			),
			examples: z.array(
				z.object({
					EN: z.string(),
					VI: z.string(),
				})
			),
		})
	),
});

export type WordNetData = z.infer<typeof WordNetDataSchema>;

export type RandomWordDetail = z.infer<typeof RandomWordDetailSchema>;

export type WordDetail = Prisma.WordGetPayload<{
	include: {
		definitions: {
			include: {
				examples: true;
				explains: true;
			};
		};
		WordNetData: true;
	};
}>;

export type ReviewWordDetail = Prisma.LastSeenWordGetPayload<{}> & {
	retention_score: number;
	priority_score: number;
	word: WordDetail;
};
