'use client';

import { useCallback, useState } from 'react';

interface Example {
	id: string;
	EN: string;
	VI: string;
}

interface ExamplePaginationState {
	examples: Example[];
	hasMore: boolean;
	total: number;
	loading: boolean;
	error: string | null;
}

export interface UseWordExamplesResult {
	exampleStates: Record<string, ExamplePaginationState>;
	loadMoreExamples: (wordId: string, definitionId: string) => Promise<void>;
	resetExamples: (definitionId: string) => void;
	getExampleState: (definitionId: string) => ExamplePaginationState;
}

const DEFAULT_STATE: ExamplePaginationState = {
	examples: [],
	hasMore: false,
	total: 0,
	loading: false,
	error: null,
};

export function useWordExamples(): UseWordExamplesResult {
	const [exampleStates, setExampleStates] = useState<Record<string, ExamplePaginationState>>({});

	const getExampleState = useCallback(
		(definitionId: string): ExamplePaginationState => {
			return exampleStates[definitionId] || DEFAULT_STATE;
		},
		[exampleStates]
	);

	const loadMoreExamples = useCallback(
		async (wordId: string, definitionId: string) => {
			const currentState = getExampleState(definitionId);

			if (currentState.loading || !currentState.hasMore) {
				return;
			}

			setExampleStates((prev) => ({
				...prev,
				[definitionId]: {
					...currentState,
					loading: true,
					error: null,
				},
			}));

			try {
				const offset = currentState.examples.length;
				const limit = 3;

				const url = new URL(`/api/words/${wordId}/examples/more`, window.location.origin);
				url.searchParams.set('definitionId', definitionId);
				url.searchParams.set('offset', offset.toString());
				url.searchParams.set('limit', limit.toString());

				const response = await fetch(url.toString());

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to load more examples');
				}

				const result = await response.json();

				setExampleStates((prev) => ({
					...prev,
					[definitionId]: {
						examples: [...currentState.examples, ...result.examples],
						hasMore: result.hasMore,
						total: result.total,
						loading: false,
						error: null,
					},
				}));
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : 'Failed to load more examples';

				setExampleStates((prev) => ({
					...prev,
					[definitionId]: {
						...currentState,
						loading: false,
						error: errorMessage,
					},
				}));
			}
		},
		[getExampleState]
	);

	const resetExamples = useCallback((definitionId: string) => {
		setExampleStates((prev) => {
			const newState = { ...prev };
			delete newState[definitionId];
			return newState;
		});
	}, []);

	return {
		exampleStates,
		loadMoreExamples,
		resetExamples,
		getExampleState,
	};
}
