'use client';

import { Button, Card, Input, Label, Translate } from '@/components/ui';
import { useTranslation, useGuidance } from '@/contexts';
import { Plus, Languages, X } from 'lucide-react';
import { useCallback, useState, useImperativeHandle, forwardRef } from 'react';

interface VocabularyInputFormProps {
	onTranslate: (words: string[]) => void;
	isLoading?: boolean;
	maxWords?: number;
}

export interface VocabularyInputFormRef {
	addWord: (word: string) => void;
}

export const VocabularyInputForm = forwardRef<VocabularyInputFormRef, VocabularyInputFormProps>(
	function VocabularyInputForm({ onTranslate, isLoading = false, maxWords = 10 }, ref) {
		const { t } = useTranslation();
		const { markUserInteraction } = useGuidance();
		const [newWord, setNewWord] = useState('');
		const [words, setWords] = useState<string[]>([]);

		const handleAddWord = useCallback(() => {
			if (!newWord.trim() || words.length >= maxWords) return;

			const trimmedWord = newWord.trim();
			if (words.includes(trimmedWord)) {
				setNewWord('');
				return;
			}

			setWords([...words, trimmedWord]);
			setNewWord('');
			markUserInteraction();
		}, [newWord, words, maxWords, markUserInteraction]);

		const handleRemoveWord = useCallback(
			(index: number) => {
				setWords(words.filter((_, i) => i !== index));
				markUserInteraction();
			},
			[words, markUserInteraction]
		);

		const handleKeyPress = useCallback(
			(e: React.KeyboardEvent<HTMLInputElement>) => {
				if (e.key === 'Enter') {
					e.preventDefault();
					handleAddWord();
				}
			},
			[handleAddWord]
		);

		const handleTranslate = useCallback(() => {
			if (words.length === 0) return;
			onTranslate(words);
			markUserInteraction();
		}, [words, onTranslate, markUserInteraction]);

		const handleClear = useCallback(() => {
			setWords([]);
			setNewWord('');
			markUserInteraction();
		}, [markUserInteraction]);

		const addWordFromExternal = useCallback(
			(word: string) => {
				const trimmedWord = word.trim();
				if (!trimmedWord || words.length >= maxWords || words.includes(trimmedWord)) return;

				setWords((prev) => [...prev, trimmedWord]);
				markUserInteraction();
			},
			[words, maxWords, markUserInteraction]
		);

		useImperativeHandle(
			ref,
			() => ({
				addWord: addWordFromExternal,
			}),
			[addWordFromExternal]
		);

		return (
			<div className="space-y-4">
				<Card className="p-7 rounded-2xl shadow-2xl bg-card">
					<div className="flex flex-col gap-3">
						<div className="flex items-center gap-2 mb-2">
							<Languages className="h-6 w-6 text-primary" />
							<Label className="text-lg font-bold text-primary tracking-tight">
								<Translate text="vocabulary_translate.input_label" />
							</Label>
						</div>

						<div className="flex gap-3 items-center">
							<div className="relative flex-1">
								<Input
									id="word-input"
									type="text"
									value={newWord}
									onChange={(e) => setNewWord(e.target.value)}
									onKeyDown={handleKeyPress}
									placeholder={t('vocabulary_translate.input_placeholder')}
									className="h-11 py-2 text-sm rounded-xl focus:ring-2 focus:ring-primary/30 bg-background text-primary shadow-inner placeholder:text-primary/60"
									aria-label={t('vocabulary_translate.input_placeholder')}
									disabled={isLoading || words.length >= maxWords}
								/>
							</div>
							<Button
								onClick={handleAddWord}
								disabled={!newWord.trim() || words.length >= maxWords || isLoading}
								loading={isLoading}
								size="sm"
								className="h-9 rounded-xl px-4 font-semibold bg-primary text-background shadow-lg hover:bg-primary/90 transition-all duration-200 flex gap-2 items-center text-sm"
							>
								<Plus className="h-4 w-4" />
								<Translate text="ui.add" />
							</Button>
						</div>

						<div className="space-y-3">
							<div className="min-h-[36px]">
								{words.length > 0 ? (
									<div className="flex flex-wrap gap-3">
										{words.map((word, index) => (
											<div key={index} className="relative">
												<span className="inline-flex items-center px-4 py-1.5 rounded-xl font-medium text-sm select-none transition-all duration-200 bg-primary text-background shadow-lg">
													{word}
													<button
														onClick={() => handleRemoveWord(index)}
														className="ml-2 hover:bg-background/20 rounded-full p-0.5 transition-colors"
														aria-label={t('ui.remove')}
														disabled={isLoading}
													>
														<X className="h-3 w-3" />
													</button>
												</span>
											</div>
										))}
									</div>
								) : (
									<p className="text-muted-foreground text-sm italic">
										<Translate text="vocabulary_translate.no_words" />
									</p>
								)}
							</div>
							<div className="text-xs text-muted-foreground">
								<Translate
									text="vocabulary_translate.word_count"
									values={{ current: words.length, max: maxWords }}
								/>
							</div>
						</div>
					</div>
				</Card>

				{words.length > 0 && (
					<div className="flex gap-3 justify-center pt-2">
						<Button
							onClick={handleClear}
							variant="outline"
							size="sm"
							disabled={isLoading}
							className="rounded-xl"
						>
							<Translate text="ui.clear" />
						</Button>
						<Button
							onClick={handleTranslate}
							disabled={words.length === 0 || isLoading}
							size="sm"
							className="rounded-xl bg-primary text-background hover:bg-primary/90 font-semibold px-6"
							loading={isLoading}
						>
							{!isLoading && <Languages className="h-4 w-4 mr-2" />}
							<Translate
								text={
									isLoading
										? 'vocabulary_translate.translating'
										: 'vocabulary_translate.translate_button'
								}
							/>
						</Button>
					</div>
				)}
			</div>
		);
	}
);
