import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { auth } from '@/lib';
import { NextRequest, NextResponse } from 'next/server';
import { z, ZodError } from 'zod';

const getMoreExamplesSchema = z.object({
	definitionId: z.string().min(1, 'Definition ID is required'),
	offset: z.number().min(0, 'Offset must be non-negative').default(0),
	limit: z.number().min(1).max(10).default(3),
});

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ wordId: string }> }
) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('User not authenticated for fetching examples.');

		const { wordId } = await params;
		const { searchParams } = new URL(request.url);

		const validatedData = getMoreExamplesSchema.parse({
			definitionId: searchParams.get('definitionId'),
			offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,
			limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 3,
		});

		const { definitionId, offset, limit } = validatedData;

		const wordService = getWordService();
		const result = await wordService.getMoreExamples(wordId, definitionId, offset, limit);

		return NextResponse.json(result);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Get more examples error:', error);
		return NextResponse.json({ error: 'Failed to fetch more examples' }, { status: 500 });
	}
}
