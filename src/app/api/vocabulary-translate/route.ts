import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'node:fs/promises';
import path from 'node:path';
import { z, ZodError } from 'zod';

const vocabularyTranslateSchema = z.object({
	words: z
		.array(z.string())
		.min(1, 'At least one word is required')
		.max(10, 'Cannot translate more than 10 words at once'),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
});

export async function POST(request: NextRequest) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('User not authenticated for vocabulary translation.');

		const body = await request.json();
		const validatedData = vocabularyTranslateSchema.parse(body);
		const { words, source_language, target_language } = validatedData;

		// Create cache file path for development
		const cacheDir = path.join(process.cwd(), '.cache', 'llm');
		const cacheKey = `vocabulary-translate-${words.join('-')}-${source_language}-${target_language}`;
		const filePath = path.join(cacheDir, `${cacheKey}.json`);

		// Check cache in development
		if (process.env.NODE_ENV === 'development') {
			try {
				const cachedData = await fs.readFile(filePath, 'utf-8');
				const cachedResult = JSON.parse(cachedData);
				console.log(`Development mode: Using cached vocabulary translation from ${filePath}`);
				return NextResponse.json(cachedResult);
			} catch (err: any) {
				console.log(`Development mode: Cache miss for ${filePath}, generating new translation`);
			}
		}

		const llmService = await getLLMService();
		const translatedWords = await llmService.translateVocabulary(words, source_language, target_language);

		// Save to cache in development
		if (process.env.NODE_ENV === 'development') {
			try {
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(translatedWords, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save vocabulary translation cache file ${filePath}:`,
					err.message
				);
			}
		}

		return NextResponse.json(translatedWords);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Vocabulary translation error:', error);
		return NextResponse.json(
			{ error: 'Failed to translate vocabulary' },
			{ status: 500 }
		);
	}
}
