'use client';

import { HeroSection } from '@/components/home';
import { <PERSON><PERSON>, Card, ScreenReaderAnnouncement, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { ArrowRight, BookOpen, Languages } from 'lucide-react';
import Link from 'next/link';

export default function HomeClient() {
	const { t } = useTranslation();

	return (
		<main role="main" aria-label={t('accessibility.home_page')}>
			<ScreenReaderAnnouncement message={t('accessibility.home')} priority="polite" />
			<div className="fixed inset-0 -z-10 bg-background" />
			<div className="space-y-16 text-center">
				<HeroSection />

				{/* Quick Actions Section */}
				<div className="space-y-8">
					<h2 className="text-3xl font-bold">
						<Translate text="home.ready_to_start" />
					</h2>
					<p className="text-muted-foreground text-lg max-w-2xl mx-auto">
						<Translate text="home.join_thousands" />
					</p>

					{/* Action Cards */}
					<div className="flex justify-center max-w-4xl mx-auto">
						<Card className="p-6 bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20 hover:border-primary/30 transition-all duration-300 max-w-md w-full">
							<div className="space-y-4">
								<div className="flex items-center justify-center">
									<BookOpen className="h-12 w-12 text-primary" />
								</div>
								<h3 className="text-xl font-semibold">
									<Translate text="home.collections" />
								</h3>
								<p className="text-muted-foreground">
									<Translate text="home.collections_description" />
								</p>
								<Link href="/collections">
									<Button
										size="lg"
										className="w-full bg-primary hover:bg-primary/90 transition-all duration-300"
									>
										<BookOpen className="size-5 mr-2" />
										<Translate text="home.start_learning_now" />
										<ArrowRight className="size-5 ml-2" />
									</Button>
								</Link>
							</div>
						</Card>
					</div>
				</div>
			</div>
		</main>
	);
}
