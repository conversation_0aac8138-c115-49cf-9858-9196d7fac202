'use client';

import {
	VocabularyInputForm,
	VocabularyInputFormRef,
} from '@/components/vocabulary-translate/vocabulary-input-form';
import { WordList } from './word-list';
import { Button, ScreenReaderAnnouncement, Translate } from '@/components/ui';
import { useTranslation, useAuth, useGuidance } from '@/contexts';
import { useCollections } from '@/hooks';
import { useWordExamples } from '@/hooks/use-word-examples';
import { useToast } from '@/contexts/toast-context';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { useCallback, useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';

interface LoadingState {
	[term: string]: {
		gettingDetail: boolean;
		adding: boolean;
		generatingExamples: boolean;
	};
}

interface VocabularyTranslateTabProps {
	collectionId: string;
	onSearchWordNetTerm?: (term: string) => void;
	wordNetSearchLoading?: Record<string, boolean>;
}

export interface VocabularyTranslateTabRef {
	addWordToTranslate: (word: string) => void;
	addWordToTranslateAndGetDetails: (word: string) => Promise<void>;
}

export const VocabularyTranslateTab = forwardRef<
	VocabularyTranslateTabRef,
	VocabularyTranslateTabProps
>(function VocabularyTranslateTab(
	{ collectionId, onSearchWordNetTerm, wordNetSearchLoading = {} },
	ref
) {
	const { t } = useTranslation();
	const { user } = useAuth();
	const { showError, showSuccess } = useToast();
	const { showGuidance } = useGuidance();
	const { currentCollection } = useCollections();
	const { loadMoreExamples } = useWordExamples();
	const vocabularyInputRef = useRef<VocabularyInputFormRef>(null);

	const [translatedWords, setTranslatedWords] = useState<WordDetail[]>([]);
	const [isTranslating, setIsTranslating] = useState(false);
	const [loadingStates, setLoadingStates] = useState<LoadingState>({});
	const [addedWords, setAddedWords] = useState<Set<string>>(new Set());
	const [addedWordIds, setAddedWordIds] = useState<Record<string, string>>({});

	// Computed loading state - true if translating or any word is getting details
	const isAnyLoading =
		isTranslating || Object.values(loadingStates).some((state) => state.gettingDetail);

	// Set up guidance for this tab
	useEffect(() => {
		showGuidance({
			titleKey: 'vocabulary_translate.guidance.title',
			steps: [
				{ key: 'vocabulary_translate.guidance.step1' },
				{ key: 'vocabulary_translate.guidance.step2' },
				{ key: 'vocabulary_translate.guidance.step3' },
			],
			tipKey: 'vocabulary_translate.guidance.tip',
			defaultOpen: false, // Don't auto-open in tab context
		});
	}, [showGuidance]);

	const handleTranslate = useCallback(
		async (words: string[], appendToList = false) => {
			if (!user || words.length === 0) return;

			setIsTranslating(true);
			try {
				const response = await fetch('/api/vocabulary-translate', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						words,
						source_language: currentCollection?.source_language || Language.EN,
						target_language: currentCollection?.target_language || Language.VI,
					}),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(
						errorData.error || t('vocabulary_translate.translation_failed')
					);
				}

				const result = await response.json();

				if (appendToList) {
					// Append new words to existing list, avoiding duplicates
					setTranslatedWords((prev) => {
						const existingTerms = new Set(prev.map((word: WordDetail) => word.term));
						const newWords = result.filter(
							(word: WordDetail) => !existingTerms.has(word.term)
						);
						return [...prev, ...newWords];
					});
				} else {
					// Replace entire list (normal translate behavior)
					setTranslatedWords(result);
				}

				showSuccess(
					t('vocabulary_translate.translation_success', { count: result.length })
				);
			} catch (error) {
				const err = error instanceof Error ? error : new Error('Translation failed');
				showError(err);
			} finally {
				setIsTranslating(false);
			}
		},
		[user, currentCollection, t, showError, showSuccess]
	);

	const handleClear = useCallback(() => {
		setTranslatedWords([]);
	}, []);

	const getLoadingState = useCallback(
		(term: string) => {
			return (
				loadingStates[term] || {
					gettingDetail: false,
					adding: false,
					generatingExamples: false,
				}
			);
		},
		[loadingStates]
	);

	const handleGetDetails = useCallback(
		async (word: { term: string }) => {
			if (!currentCollection) return;

			// Set loading state
			setLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], gettingDetail: true },
			}));

			try {
				const response = await fetch('/api/llm/word-details', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						term: word.term,
						source_language: currentCollection.source_language,
						target_language: currentCollection.target_language,
					}),
				});

				if (!response.ok) {
					throw new Error('Failed to get word details');
				}

				const wordDetail: WordDetail = await response.json();

				// Update translated words with detailed information
				setTranslatedWords((prev) =>
					prev.map((w) => (w.term === word.term ? wordDetail : w))
				);
			} catch (error) {
				const err =
					error instanceof Error ? error : new Error('Failed to get word details');
				showError(err);
			} finally {
				// Clear loading state
				setLoadingStates((prev) => ({
					...prev,
					[word.term]: { ...prev[word.term], gettingDetail: false },
				}));
			}
		},
		[currentCollection, showError]
	);

	const handleAddToCollection = useCallback(
		async (word: { term: string }) => {
			if (!currentCollection) return;

			// Set loading state
			setLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], adding: true },
			}));

			try {
				const response = await fetch(`/api/collections/${currentCollection.id}/words`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						term: word.term,
						language: currentCollection.target_language,
					}),
				});

				if (!response.ok) {
					throw new Error('Failed to add word to collection');
				}

				const result = await response.json();

				// Find the newly added word ID
				const addedWord = result.words?.find((w: any) => w.term === word.term);
				if (addedWord?.id) {
					setAddedWordIds((prev) => ({ ...prev, [word.term]: addedWord.id }));
				}

				setAddedWords((prev) => new Set([...prev, word.term]));
				showSuccess(t('words.word_added'));
			} catch (error) {
				const err = error instanceof Error ? error : new Error('Failed to add word');
				showError(err);
			} finally {
				// Clear loading state
				setLoadingStates((prev) => ({
					...prev,
					[word.term]: { ...prev[word.term], adding: false },
				}));
			}
		},
		[currentCollection, t, showError, showSuccess]
	);

	const handleUndoWordAddition = useCallback(
		async (word: { term: string }) => {
			if (!currentCollection) return;

			const wordId = addedWordIds[word.term];
			if (!wordId) {
				showError(new Error('Cannot find word ID for removal'));
				return;
			}

			try {
				const response = await fetch(`/api/collections/${currentCollection.id}/words`, {
					method: 'DELETE',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						wordIds: [wordId],
					}),
				});

				if (!response.ok) {
					throw new Error('Failed to remove word from collection');
				}

				setAddedWords((prev) => {
					const newSet = new Set(prev);
					newSet.delete(word.term);
					return newSet;
				});
				setAddedWordIds((prev) => {
					const newIds = { ...prev };
					delete newIds[word.term];
					return newIds;
				});
				showSuccess(t('words.word_removed'));
			} catch (error) {
				const err = error instanceof Error ? error : new Error('Failed to remove word');
				showError(err);
			}
		},
		[currentCollection, addedWordIds, t, showError, showSuccess]
	);

	const handleGenerateExamples = useCallback(
		async (word: { term: string }) => {
			if (!currentCollection) return;

			// Find the word in translated words to get wordId and definitionId
			const translatedWord = translatedWords.find((w) => w.term === word.term);
			if (!translatedWord?.id || !translatedWord.definitions?.[0]?.id) {
				showError(
					new Error('Word must be saved to collection first before loading examples')
				);
				return;
			}

			// Set loading state
			setLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], generatingExamples: true },
			}));

			try {
				await loadMoreExamples(translatedWord.id, translatedWord.definitions[0].id);
				showSuccess(t('words.examples_loaded'));
			} catch (error) {
				const err = error instanceof Error ? error : new Error('Failed to load examples');
				showError(err);
			} finally {
				// Clear loading state
				setLoadingStates((prev) => ({
					...prev,
					[word.term]: { ...prev[word.term], generatingExamples: false },
				}));
			}
		},
		[currentCollection, translatedWords, loadMoreExamples, t, showError, showSuccess]
	);

	// Expose methods to parent component
	useImperativeHandle(
		ref,
		() => ({
			addWordToTranslate: (word: string) => {
				if (vocabularyInputRef.current) {
					vocabularyInputRef.current.addWord(word);
				}
			},
			addWordToTranslateAndGetDetails: async (word: string) => {
				// Add word to translate form
				if (vocabularyInputRef.current) {
					vocabularyInputRef.current.addWord(word);
				}

				// Trigger translation to get details with append mode
				await handleTranslate([word], true);
			},
		}),
		[handleTranslate]
	);

	if (!currentCollection) return null;

	return (
		<div className="space-y-6">
			<ScreenReaderAnnouncement message={t('vocabulary_translate.page_title')} />

			{/* Input Form */}
			<VocabularyInputForm
				ref={vocabularyInputRef}
				onTranslate={handleTranslate}
				isLoading={isAnyLoading}
				maxWords={10}
			/>

			{/* Results */}
			{translatedWords.length > 0 && (
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<h2 className="text-xl font-semibold flex items-center gap-2">
							<Translate text="vocabulary_translate.results_title" />
							{Object.values(loadingStates).some((state) => state.gettingDetail) && (
								<span className="text-sm text-muted-foreground font-normal">
									<Translate text="words.getting_details" />
								</span>
							)}
						</h2>
						<Button
							onClick={handleClear}
							variant="outline"
							size="sm"
							className="rounded-xl"
							disabled={isAnyLoading}
						>
							<Translate text="ui.clear" />
						</Button>
					</div>

					<WordList
						words={translatedWords.map((word) => ({
							term: word.term,
							partOfSpeech: word.definitions[0]?.pos || [],
							meaning:
								word.definitions[0]?.explains?.map((explain) => ({
									EN: explain.EN,
									VI: explain.VI,
								})) || [],
						}))}
						detailedWords={translatedWords.reduce((acc, word) => {
							acc[word.term] = word;
							return acc;
						}, {} as Record<string, WordDetail>)}
						onGetDetails={handleGetDetails}
						getLoadingState={getLoadingState}
						onAddToCollection={handleAddToCollection}
						onUndoWordAddition={handleUndoWordAddition}
						onGenerateExamples={handleGenerateExamples}
						addedWords={addedWords}
						sourceLanguage={currentCollection.source_language}
						targetLanguage={currentCollection.target_language}
						className="mt-6"
						onSearchWordNetTerm={onSearchWordNetTerm}
						wordNetSearchLoading={wordNetSearchLoading}
					/>
				</div>
			)}
		</div>
	);
});
