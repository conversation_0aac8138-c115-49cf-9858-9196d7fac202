'use client';

import { WordCard } from './word-card';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { memo, useCallback } from 'react';
import { cn } from '@/lib';

interface SelectableWordCardProps {
	word: WordDetail;
	onDeleteWord?: () => void;
	isDeleting?: boolean;
	className?: string;
	isReviewMode?: boolean;
	showSourceLanguage?: boolean;
	onToggleTargetLanguage?: () => void;
	defaultExpanded?: boolean;
	sourceLanguage: Language;
	targetLanguage: Language;
	// Selection props
	isSelected?: boolean;
	onSelectionChange?: (wordId: string, selected: boolean) => void;
	selectionMode?: boolean;
	// WordNet search functionality
	onSearchWordNetTerm?: (term: string) => void;
}

function SelectableWordCardComponent({
	word,
	onDeleteWord,
	isDeleting,
	className,
	isReviewMode = false,
	showSourceLanguage = true,
	onToggleTargetLanguage,
	defaultExpanded,
	sourceLanguage,
	targetLanguage,
	isSelected = false,
	onSelectionChange,
	selectionMode = false,
	onSearchWordNetTerm,
}: SelectableWordCardProps) {
	const handleSelectionChange = useCallback(
		(checked: boolean) => {
			if (onSelectionChange) {
				onSelectionChange(word.id, checked);
			}
		},
		[word.id, onSelectionChange]
	);

	const handleClick = useCallback(
		(event: React.MouseEvent) => {
			// Don't trigger selection if clicking on interactive elements
			const target = event.target as HTMLElement;
			const isInteractiveElement =
				target.tagName === 'BUTTON' ||
				target.closest('button') ||
				target.tagName === 'A' ||
				target.closest('a') ||
				target.tagName === 'INPUT' ||
				target.closest('input') ||
				target.tagName === 'SELECT' ||
				target.closest('select') ||
				// Also check for elements with click handlers or interactive roles
				target.hasAttribute('onclick') ||
				target.getAttribute('role') === 'button' ||
				target.closest('[role="button"]');

			if (!isInteractiveElement && selectionMode) {
				handleSelectionChange(!isSelected);
			}
		},
		[selectionMode, isSelected, handleSelectionChange]
	);

	return (
		<div
			className={cn(
				!selectionMode && 'transition-all duration-200',
				selectionMode && 'cursor-pointer',
				isSelected &&
					'ring-2 ring-primary ring-offset-2 ring-offset-background border-2 border-primary rounded-lg',
				className
			)}
			onClick={handleClick}
		>
			<WordCard
				word={word}
				onDeleteWord={onDeleteWord}
				isDeleting={isDeleting}
				isReviewMode={isReviewMode}
				showSourceLanguage={showSourceLanguage}
				onToggleTargetLanguage={onToggleTargetLanguage}
				defaultExpanded={defaultExpanded}
				sourceLanguage={sourceLanguage}
				targetLanguage={targetLanguage}
				selectionMode={selectionMode}
				onSearchWordNetTerm={onSearchWordNetTerm}
			/>
		</div>
	);
}

// Memoization comparison function
const arePropsEqual = (
	prevProps: SelectableWordCardProps,
	nextProps: SelectableWordCardProps
): boolean => {
	return (
		prevProps.word.id === nextProps.word.id &&
		prevProps.word.term === nextProps.word.term &&
		prevProps.word.updated_at === nextProps.word.updated_at &&
		prevProps.isDeleting === nextProps.isDeleting &&
		prevProps.isReviewMode === nextProps.isReviewMode &&
		prevProps.showSourceLanguage === nextProps.showSourceLanguage &&
		prevProps.defaultExpanded === nextProps.defaultExpanded &&
		prevProps.sourceLanguage === nextProps.sourceLanguage &&
		prevProps.targetLanguage === nextProps.targetLanguage &&
		prevProps.isSelected === nextProps.isSelected &&
		prevProps.selectionMode === nextProps.selectionMode &&
		prevProps.onDeleteWord === nextProps.onDeleteWord &&
		prevProps.onToggleTargetLanguage === nextProps.onToggleTargetLanguage &&
		prevProps.onSelectionChange === nextProps.onSelectionChange &&
		prevProps.onSearchWordNetTerm === nextProps.onSearchWordNetTerm
	);
};

export const SelectableWordCard = memo(SelectableWordCardComponent, arePropsEqual);
