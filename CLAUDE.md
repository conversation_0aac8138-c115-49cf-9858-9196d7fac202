## 🧠 VOCAB PROJECT – LLM MEMORY (COMPACT)

## 📋 PROJECT OVERVIEW

**Vocab** là một ứng dụng học từ vựng AI-powered được xây dựng với Next.js 15, tập trung vào việc mở rộng từ vựng thông qua spaced repetition, nội dung được tạo bởi AI và các bộ sưu tập học tập có tổ chức.

### ✅ QUICK RULES

-   Test tại `localhost:6001` mà không cần `yarn dev`.
-   <PERSON>hi thêm translation key, luôn cập nhật `translation context`.
-   Dự án áp dụng Domain-Driven Design (DDD).
-   Khi viết content trong html thì nhớ encode các ký tự như " hay '.
-   Khi tạo doc luôn lưu vào thư mục docs.
-   <PERSON><PERSON> xong task phải chạy npx tsc và fix errors.
-   <PERSON><PERSON> tạo một bảng mới hay sửa schema, lu<PERSON><PERSON> luôn cập nhật lại repositories, services, models, dto liên quan.
-   Luôn luôn đảm bảo type-safe trong toàn bộ project.
-   Luôn đảm bảo type-safe theo luồng từ backend đến frontend.
-   Hạn chế dùng cast như "as" trong dự án.
-   Ưu tiên quản lý state thông qua context/hooks.

## 🏗️ TECHNOLOGY STACK

### Frontend

-   **Framework**: Next.js 15 với App Router
-   **Language**: TypeScript (strict mode)
-   **Styling**: Tailwind CSS v4 với custom animations
-   **UI Components**: Radix UI primitives với design system tùy chỉnh
-   **State Management**: React Context API + Custom hooks
-   **Icons**: Lucide React
-   **Package Manager**: Yarn v4.9.2 (KHÔNG sử dụng npm)
-   **Notifications**: Sonner toast system

### Backend

-   **Runtime**: Node.js với Next.js API routes
-   **Database**: PostgreSQL với Prisma ORM
-   **Authentication**: JWT với multiple providers (Google, Username/Password)
-   **AI Integration**: OpenAI GPT-4o-mini + Google Gemini (via Genkit)
-   **Caching**: Node-cache với file-based persistence + Redis (ioredis)
-   **Security**: Security headers, CSRF protection, rate limiting

### Infrastructure

-   **Environment**: Environment-based configuration
-   **PWA**: Service worker và manifest cho offline support
-   **Testing**: Jest + Vitest + Playwright + MSW

## 🏛️ ARCHITECTURE PATTERNS

### Clean Architecture Implementation

Dự án áp dụng Domain-Driven Design (DDD) với kiến trúc phân lớp rõ ràng:

```
src/
├── app/                     # Next.js App Router - Presentation Layer
├── backend/                 # Server-side Business Logic
│   ├── services/           # Business Logic Layer (21 services)
│   ├── repositories/       # Data Access Layer (9 repositories)
│   ├── middleware/         # Server Middleware
│   ├── schemas/            # MongoDB Schemas & Validation
│   ├── errors/             # Custom Error Classes
│   ├── utils/              # Backend Utilities
│   ├── config/             # Backend Configuration
│   └── wire.ts            # Dependency Injection Container
├── components/             # UI Components Library
├── contexts/              # React Context Providers (11 contexts)
├── hooks/                 # Custom React Hooks (9 hooks)
├── lib/                   # Shared Utilities & Integrations
├── models/                # Domain Models (5 models)
├── types/                 # TypeScript Type Definitions
├── config/                # Configuration Management
├── constants/             # Application Constants
├── providers/             # React Providers
├── styles/                # Custom CSS Styles
└── test/                  # Testing Infrastructure
```

### Key Design Principles

1. **Separation of Concerns**: Tách biệt rõ ràng giữa API, Service và Repository layers
2. **Dependency Injection**: Centralized service wiring trong `wire.ts`
3. **Type Safety**: Sử dụng TypeScript toàn diện với Prisma-generated types
4. **Security First**: Multiple security layers với validation, rate limiting, CSRF protection
5. **Performance**: Caching strategies, optimized LLM usage, skeleton loading states

## 🔧 BACKEND ARCHITECTURE

### Layer Organization

#### 1. Service Layer (`src/backend/services`)

-   **Responsibility**: Business logic, orchestration
-   **Pattern**: Service interfaces với implementation classes
-   **Key Services**:
    -   `AuthService`: Authentication và user session management
    -   `CollectionService`: Collection CRUD và word/term management
    -   `WordService`: Word search, retrieval, vocabulary operations
    -   `LLMService`: OpenAI GPT-4o-mini integration cho AI features
    -   `GeminiService`: Google Gemini integration via Genkit
    -   `CacheService`: Multi-tier caching operations
    -   `RedisCacheService`: Redis-based caching
    -   `SemanticCacheService`: Semantic similarity caching
    -   `ServerCacheService`: Server-side caching
    -   `CacheFactoryService`: Cache strategy selection
    -   `UserService`: User profile và settings management
    -   `KeywordService`: Keyword search và management
    -   `FeedbackService`: User feedback collection
    -   `LastSeenWordService`: Spaced repetition tracking
    -   `CollectionStatsService`: Collection analytics
    -   `TokenMonitorService`: AI token usage monitoring
    -   `BatchProcessorService`: Batch processing operations
    -   `ModelSelectorService`: AI model selection
    -   `PromptOptimizerService`: Prompt optimization

#### 2. Repository Layer (`src/backend/repositories`)

-   **Responsibility**: Data access, Prisma operations
-   **Pattern**: Repository pattern với base repository
-   **Features**: Support cả PostgreSQL và MongoDB
-   **Base Repository**: Tất cả repositories extend `BaseRepositoryImpl<T>`
-   **Methods**: `findById`, `findOne`, `find`, `create`, `update`, `delete`
-   **Dual Database**: Có implementation riêng cho PostgreSQL và MongoDB

#### 3. Dependency Injection (`src/backend/wire.ts`)

-   **Pattern**: Factory functions với lazy initialization
-   **Benefits**: Loose coupling, testability, service lifecycle management
-   **Usage**: `getCollectionService()`, `getUserRepository()`, etc.
-   **Singleton Pattern**: Tất cả services và repositories là singletons
-   **Database Switching**: Tự động chọn repository implementation dựa trên feature flags

### Database Strategy

-   **Primary**: PostgreSQL với Prisma ORM
-   **Schema**: Comprehensive schema với User, Collection, Word, Paragraph models

## 🎨 FRONTEND ARCHITECTURE

### Component Organization

#### 1. App Router Structure (`src/app`)

-   **Layout**: Root layout với provider hierarchy
-   **Pages**: Client/server component separation
-   **Routing**: File-based routing với dynamic routes
-   **Structure**:
    ```
    src/app/
    ├── layout.tsx              # Root layout với PWA support
    ├── page.tsx                # Home page với animations
    ├── globals.css             # Global styles
    ├── components/             # App-specific components
    └── collections/            # Collections feature pages
        ├── [id]/              # Dynamic collection routes
        │   ├── page.tsx       # Collection detail page
        │   ├── paragraph/     # Paragraph practice
        │   └── components/    # Collection-specific components
        └── page.tsx           # Collections listing page
    ```

#### 2. Component Layer (`src/components`)

-   **UI Components**: Reusable primitives trong `src/components/ui`
    -   Accessibility-first design với ARIA support
    -   Consistent styling với Tailwind CSS
    -   Export pattern: Tất cả components export qua `index.ts`
-   **Feature Components**: Domain-specific components
    -   Home components: HeroSection, CollectionsSection, etc.
    -   Auth components: AuthGuard, LoginForm, etc.
    -   Floating UI: FloatingUIManager, SimpleFloatingProvider

#### 3. State Management

-   **Global State**: React Context providers
    -   AuthContext: User authentication state
    -   TranslationContext: Language và translation state
    -   LoadingContext: Loading states với scoped loading
    -   ToastContext: Toast notifications
    -   ThemeContext: Dark/light theme
-   **Local State**: Custom hooks
    -   `useCollections`: Collection management
    -   `useGracefulDegradation`: Feature degradation
-   **Server State**: SWR pattern với API integration
    -   Caching strategies
    -   Optimistic updates
    -   Error handling

### Internationalization System

#### Translation Architecture

-   **Translation Files**: Organized theo domain trong `src/contexts/translations/`
-   **Translation Keys**: Structured keys như `nav.home`, `collections.create`
-   **Language Support**: English (EN) và Vietnamese (VI)
-   **Type Safety**: TypeScript types cho translation keys

## 🔐 SECURITY IMPLEMENTATION

### Multi-Layer Security

#### 1. Middleware Level (`src/middleware.ts`)

-   **Authentication**: JWT token verification cho tất cả API routes
-   **Public Endpoints**: `/api/auth/*` không cần authentication
-   **Rate Limiting**: IP-based request throttling
-   **Security Headers**: X-Content-Type-Options, X-Frame-Options, X-XSS-Protection
-   **CORS**: Configurable origin validation

#### 2. API Level (`src/lib/api-error-middleware.ts`)

-   **Input Validation**: Zod schema validation cho tất cả API endpoints
-   **Error Handling**: Structured error responses với request tracing
-   **Authentication**: Protected route middleware
-   **Middleware Functions**:
    -   `withErrorHandling`: Error boundary cho API routes
    -   `withValidation`: Schema validation
    -   `withRateLimit`: Rate limiting
    -   `withAuth`: Authentication checks
    -   `withCors`: CORS handling

#### 3. Authentication Flow

-   **JWT Tokens**: HttpOnly cookies với secure flags
-   **Multiple Providers**:
    -   Telegram Bot authentication
    -   Google OAuth (feature flag)
    -   Username/Password authentication
-   **Token Management**:
    -   Automatic refresh
    -   Secure storage trong HttpOnly cookies
    -   Environment-based expiration

#### 4. Error Handling System

-   **Comprehensive Error Management**: `src/lib/error-handling.ts`
-   **Error Types**: AppError, ValidationError, NetworkError, etc.
-   **Error Logging**: Centralized error logger với context
-   **Error Boundaries**: React error boundaries cho UI
-   **Graceful Degradation**: Feature degradation khi có lỗi

## ⚡ PERFORMANCE OPTIMIZATION

### Caching Strategy

#### 2. Database Caching

-   **In-memory**: Node-cache cho frequently accessed data
-   **Query Optimization**: Prisma query optimization với includes
-   **Connection Pooling**: Database connection management
-   **Dual Database**: Caching strategy cho cả PostgreSQL và MongoDB

#### 3. Frontend Performance

-   **Loading States**: Comprehensive skeleton components:
    -   `ListSkeleton`, `HomeSkeleton`, `CollectionsSkeleton`
    -   `PageSkeleton`, `StatsSkeleton`, `PracticeSessionSkeleton`
-   **Code Splitting**: Dynamic imports cho heavy components
-   **Image Optimization**: Next.js Image component với optimization
-   **Bundle Optimization**: Turbopack cho fast development builds

### LLM Optimization System

#### 1. Model Selection (`src/backend/services/llm.service.ts`)

-   **Adaptive Model Selection**: Chọn model dựa trên complexity
-   **Cost Optimization**: Balance giữa cost và quality
-   **Quality Threshold**: Minimum quality requirements
-   **Latency Threshold**: Maximum response time limits

#### 2. Token Management

-   **Budget Limits**: Daily và monthly token budgets
-   **Cost Alerts**: Threshold-based cost alerting
-   **Usage Tracking**: Comprehensive token usage monitoring
-   **Estimation**: Token estimation trước khi gọi API

#### 3. Prompt Optimization

-   **Compression Techniques**: Reduce prompt size
-   **Template Optimization**: Optimized prompt templates
-   **Batch Processing**: Batch multiple requests
-   **Context Management**: Efficient context handling

#### 4. Monitoring & Analytics

-   **Usage Tracking**: Track token usage, costs, performance
-   **Performance Metrics**: Latency, success rates, quality scores
-   **Error Monitoring**: LLM API errors và fallbacks
-   **Optimization Insights**: Data-driven optimization recommendations

## 🧪 TESTING STRATEGY

### Test Types & Configuration

#### 1. Unit Tests

-   **Jest**: Component và service testing (`jest.config.js`)
-   **Vitest**: Fast unit testing với HMR (`vitest.config.ts`)
-   **Coverage**: 70% threshold cho lines, functions, branches, statements
-   **Test Files**: `*.test.ts`, `*.test.tsx`, `*.spec.ts`, `*.spec.tsx`

#### 2. Integration Tests

-   **API Testing**: API endpoint testing với real database
-   **Service Integration**: Service layer integration tests
-   **Database Integration**: Repository layer testing

#### 3. E2E Tests

-   **Playwright**: Cross-browser E2E testing (`playwright.config.ts`)
-   **Test Browsers**: Chrome, Firefox, Safari, Mobile Chrome
-   **Test Patterns**: User workflows, critical paths
-   **CI Integration**: Automated E2E testing trong GitHub Actions

### Test Environment Setup

-   **Environment Variables**: Test-specific environment configuration
-   **Database Setup**: Separate test databases
-   **Mock Services**: Mock external services (OpenAI, etc.)
-   **Test Data**: Seed data cho testing

## 📝 CODING CONVENTIONS

### File Naming Conventions

#### 1. Components

-   **React Components**: PascalCase (`UserProfile.tsx`, `CollectionCard.tsx`)
-   **UI Components**: PascalCase (`Button.tsx`, `Dialog.tsx`)
-   **Page Components**: PascalCase (`HomePage.tsx`, `CollectionPage.tsx`)

#### 2. Backend Files

-   **Services**: kebab-case (`collection.service.ts`, `auth.service.ts`)
-   **Repositories**: kebab-case (`user.repository.ts`, `word.repository.ts`)
-   **API Routes**: kebab-case (`auth.api.ts`, `collection.api.ts`)
-   **Middleware**: kebab-case (`auth.middleware.ts`)

#### 3. Other Files

-   **Hooks**: camelCase với `use` prefix (`useCollections.ts`, `useAuth.ts`)
-   **Utilities**: kebab-case (`token.util.ts`, `validation.util.ts`)
-   **Types**: kebab-case (`user.types.ts`, `api.types.ts`)
-   **Constants**: kebab-case (`loading-scopes.ts`, `error-codes.ts`)

## 🔄 DEPLOYMENT & OPERATIONS

### Vercel Configuration

#### 2. Security Headers

-   **X-Content-Type-Options**: `nosniff`
-   **X-Frame-Options**: `DENY`
-   **X-XSS-Protection**: `1; mode=block`

### Environment Management

#### 1. Configuration System (`src/config/config.ts`)

-   **Server Config**: Port, environment settings
-   **Auth Config**: JWT settings, OAuth configurations
-   **LLM Config**: OpenAI settings, optimization parameters
-   **Feature Flags**: Environment-based feature toggles

#### 3. Database Configuration

-   **PostgreSQL**: Primary database với Prisma
-   **MongoDB**: Migration target với feature flags
-   **Dual Database**: Support cả hai databases
-   **Connection Management**: Centralized database manager

---

**Tài liệu này nên được cập nhật thường xuyên để phản ánh kiến trúc hiện tại của dự án.**
